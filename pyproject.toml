[project]
name = "pmcp"
version = "0.1.0"
description = "Provably Model Context Protocol"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "alembic>=1.16.5",
    "asyncpg==0.30.0",
    "click>=8.2.1",
    "fastmcp==2.12.0",
    "loguru==0.7.3",
    "psycopg2-binary>=2.9.10",
    "pydantic==2.11.7",
    "pydantic-settings==2.10.1",
    "python-dotenv>=1.1.1",
    "sqlmodel==0.0.24",
]
