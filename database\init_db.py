from loguru import logger
from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError
from sqlalchemy.ext.asyncio import create_async_engine


from action_server.core.config import Config
from database.session import db_async_engine
from database.base import db_metadata


async def _initialize_database_if_not_exist():

    logger.info(f"Ensuring the database '{Config.DB_URL.database}' exists...")
    engine = create_async_engine(Config.DB_URL, isolation_level="AUTOCOMMIT")
    db_name = Config.DB_URL.database
    async with engine.connect() as conn:
        try:
            result = await conn.execute(text("SELECT 1 FROM pg_database WHERE datname = :db_name"), {"db_name": db_name})
            db_exists = result.scalar_one_or_none()
            if not db_exists:
                logger.info(f"Database '{db_name}' does not exist. Attempting to create.")
                await conn.execute(text(f'CREATE DATABASE "{db_name}"'))
                logger.info(f"Database '{db_name}' created successfully.")
        except ProgrammingError as e:
            # PostgreSQL error code for "duplicate_database" is 42P04
            if "42P04" in str(e) or "already exists" in str(e).lower():
                logger.warning("Database effectively exists (creation attempt failed as it already exists).")
            else:
                logger.error(f"Programming error during database check/creation for '{db_name}': {e}")
                raise
        except Exception as e:
            logger.error(f"Unexpected error ensuring database '{db_name}' exists: {e}")
            raise
        finally:
            await conn.close()


async def create_tables() -> None:
    async with db_async_engine.begin() as conn:
        from shared.models import action_history # noqa: F401
        await conn.run_sync(db_metadata.create_all)


async def init_db() -> None:
    if not Config.IS_PROD_LIKE:
        logger.info(f"Database initialization in {Config.ENVIRONMENT} environment.")
        await _initialize_database_if_not_exist()
        
    await create_tables()