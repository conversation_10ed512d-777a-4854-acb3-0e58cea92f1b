"""Initial migration with ActionHistory model

Revision ID: a8b93384182c
Revises: 
Create Date: 2025-09-05 17:41:32.740027

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = 'a8b93384182c'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create action_history table
    op.create_table(
        'action_history',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('parent_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('session_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
        sa.Column('agent_identity', sa.String(length=255), nullable=False),
        sa.Column('actor', sa.String(length=255), nullable=False),
        sa.Column('action_type', sa.String(length=255), nullable=False),
        sa.Column('target_mcp', sa.String(length=255), nullable=True),
        sa.Column('request_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('response_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('proof_bytes_reference', sa.Text(), nullable=True),
        sa.Column('is_provable', sa.Boolean(), nullable=False),
        sa.Column('status', sa.String(length=50), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('ttl_expiry', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['parent_id'], ['action_history.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes
    op.create_index('id_idx', 'action_history', ['id'], unique=False)
    op.create_index('session_id_idx', 'action_history', ['session_id'], unique=False)
    op.create_index('timestamp_idx', 'action_history', ['timestamp'], unique=False)
    op.create_index('action_type_idx', 'action_history', ['action_type'], unique=False)


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes
    op.drop_index('action_type_idx', table_name='action_history')
    op.drop_index('timestamp_idx', table_name='action_history')
    op.drop_index('session_id_idx', table_name='action_history')
    op.drop_index('id_idx', table_name='action_history')

    # Drop table
    op.drop_table('action_history')
