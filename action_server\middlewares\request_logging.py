import time
from typing import Any
import uuid
from loguru import logger
import pydantic_core
from fastmcp.server.middleware import Middleware, MiddlewareContext, CallNext


def default_serializer(data: Any) -> str:
    return pydantic_core.to_json(data, fallback=str).decode()


class RequestLoggingMiddleware(Middleware):
    async def on_message(self, context: MiddlewareContext, call_next: CallNext):
        request_id = str(uuid.uuid4())
        max_payload_length = 1000

        with logger.contextualize(request_id=request_id):
            start_time = time.perf_counter()

            payload = default_serializer(context.message)
            if len(payload) > max_payload_length:
                payload = payload[:max_payload_length] + "..."

            request_details: dict[str, Any] = {
                "method": context.method or "unknown",
                "source": context.source,
                "message_type": context.type,
                "timestamp": context.timestamp.isoformat(),
                "payload": payload,
            }

            logger.info("Processing message", **request_details)

            result = await call_next(context)

            process_time_ms = round((time.perf_counter() - start_time) * 1000, 3)
            response_details: dict[str, Any] = {
                **request_details,
                "duration_ms": process_time_ms,
            }

            logger.info("Completed message", **response_details)
            return result
