import uuid
from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import Column
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field

from database.base import BaseModel


class ActionHistory(BaseModel, table=True):
    """
    Model to store the history of actions performed by agents.
    """

    __tablename__: str = "action_history"

    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)

    # FK to id, NULL for initial client request
    parent_id: Optional[uuid.UUID] = Field(
        default=None, foreign_key="action_history.id"
    )

    # Unique ID for an agent's workflow
    session_id: uuid.UUID = Field(index=True)

    # Timestamp with timezone, defaults to current time
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc), index=True
    )

    # Agent identity from ACK
    agent_identity: str = Field(max_length=255)

    # Actor performing the action (e.g., 'Client', 'PMCP_ActionServer')
    actor: str = Field(max_length=255)

    # Type of action (e.g., 'ClientRequest', 'ExternalMCPCall', 'PMCPInternalLogic', 'ProverRequest')
    action_type: str = Field(max_length=255, index=True)

    # Which MCP was targeted
    target_mcp: Optional[str] = Field(default=None, max_length=255)

    # Request payload as JSON
    request_payload: Optional[dict] = Field(sa_column=Column(JSONB))

    # Response payload as JSON
    response_payload: Optional[dict] = Field(sa_column=Column(JSONB))

    proof_bytes_reference: Optional[str] = Field(default=None)

    # Whether this action is provable
    is_provable: bool = Field(default=False)

    # Status of the action
    status: Optional[str] = Field(default=None, max_length=50)

    # Error message if any
    error_message: Optional[str] = Field(default=None)

    # TTL expiry timestamp
    ttl_expiry: Optional[datetime] = Field(default=None)
