import uuid
from datetime import datetime

from sqlmodel import Field, SQLModel, Column

class ActionHistory(SQLModel, table=True):
    """
    Model to store the history of actions performed by agents.
    """
    
    __tablename__: str = "action_history"
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, index=True)
    - `id UUID PRIMARY KEY DEFAULT gen_random_uuid()` 
    - `parent_id UUID` (FK to `id`, NULL for initial client request) 
    - `session_id UUID` (Unique ID for an agent's workflow) 
    - `timestamp TIMESTAMPTZ DEFAULT NOW()` 
    - `agent_identity VARCHAR(255) NOT NULL` (From ACK) 
    - `actor VARCHAR(255) NOT NULL` (e.g., 'Client', 'PMCP_ActionServer') 
    - `action_type VARCHAR(255) NOT NULL` (e.g., 'ClientRequest', 'ExternalMCPCall', 'PMCPInternalLogic', 'ProverRequest') 
    - `target_mcp VARCHAR(255)` (Which MCP was targeted) 
    - `request_payload JSONB` 
    - `response_payload JSONB` 
    - `proof_bytes_reference TEXT` (Reference from external MCP, initially a hash) 
    - `is_provable BOOLEAN DEFAULT FALSE` 
    - `status VARCHAR(50)` 
    - `error_message TEXT` 
    - `ttl_expiry TIMESTAMPTZ` 
