import asyncio
import json
import time
from typing import Any

import pydantic_core
from fastmcp.server.middleware import CallNext, Middleware, MiddlewareContext
from loguru import logger
from mcp import Mcp<PERSON>rror
from mcp.types import ErrorData

from shared.exceptions import (
    ContentTooLargeError,
    InvalidRequestError,
    MethodNotFoundError,
    ParseError,
    RequestCancelledError,
    ResourceUnavailableError,
)


def default_serializer(data: Any) -> str:
    return pydantic_core.to_json(data, fallback=str).decode()

def map_error_to_mcp_error(error: Exception) -> Exception:
    """Transform non-MCP errors to proper MCP errors."""
    if isinstance(error, McpError):
        return error

    # Map common exceptions to appropriate MCP error codes
    error_type = type(error)

    if error_type in (
        ResourceUnavailableError,
        ConnectionError,
        TimeoutError,
        asyncio.TimeoutError,
    ):
        # Resource Unavailable (-32000): Temporary resource failure
        return McpError(
            ErrorData(code=-32000, message=f"Resource unavailable: {str(error)}")
        )
    elif error_type in (FileNotFoundError, KeyError):
        return McpError(
            ErrorData(code=-32001, message=f"Resource not found: {str(error)}")
        )
    elif error_type is PermissionError:
        return McpError(
            ErrorData(code=-32002, message=f"Permission denied: {str(error)}")
        )
    elif error_type is NotImplementedError:
        return McpError(
            ErrorData(code=-32003, message=f"Feature not implemented: {str(error)}")
        )
    elif error_type is InvalidRequestError:
        # Invalid Request (-32600): Missing required fields (jsonrpc, method, id)
        return McpError(
            ErrorData(
                code=-32600,
                message=f"Invalid request: Missing required fields - {str(error)}",
            )
        )
    elif error_type in (AttributeError, MethodNotFoundError):
        # Method Not Found (-32601): Unknown method called
        return McpError(
            ErrorData(code=-32601, message=f"Method not found: {str(error)}")
        )
    elif error_type in (ValueError, TypeError):
        # Invalid Params (-32602): Parameter validation failed
        return McpError(
            ErrorData(code=-32602, message=f"Invalid params: {str(error)}")
        )
    elif error_type in (RuntimeError, SystemError, MemoryError, OSError):
        # Internal Error (-32603): Server exception
        return McpError(
            ErrorData(
                code=-32603,
                message="Internal error occurred while processing your request",
            )
        )
    elif error_type in (json.JSONDecodeError, ParseError):
        # Parse Error (-32700): Invalid JSON received
        return McpError(
            ErrorData(
                code=-32700,
                message=f"Parse error: Invalid JSON received - {str(error)}",
            )
        )
    elif error_type in (asyncio.CancelledError, RequestCancelledError):
        # Request Cancelled (-32800): Client cancelled operation
        return McpError(
            ErrorData(code=-32800, message=f"Request cancelled: {str(error)}")
        )
    elif error_type in (ContentTooLargeError, OverflowError):
        # Content Too Large (-32801): Payload exceeds limits
        return McpError(
            ErrorData(code=-32801, message=f"Content too large: {str(error)}")
        )
    else:
        # Fallback for any other exceptions
        return McpError(
            ErrorData(
                code=-32603,
                message="Internal error occurred while processing your request",
            )
        )

class ErrorHandlingMiddleware(Middleware):

    async def on_message(self, context: MiddlewareContext, call_next: CallNext):
        start_time = time.perf_counter()
        max_payload_length = 1000
        payload = default_serializer(context.message)

        if len(payload) > max_payload_length:
            payload = payload[:max_payload_length] + "..."

        request_details: dict[str, Any] = {
            "method": context.method or "unknown",
            "source": context.source,
            "message_type": context.type,
            "timestamp": context.timestamp.isoformat(),
            "payload": payload,
        }

        try:
            return await call_next(context)
        except Exception as e:
            process_time_ms = round((time.perf_counter() - start_time) * 1000, 3)

            error_type = type(e).__name__
            error_log_details = {
                **request_details,
                "duration_ms": process_time_ms,
                "error_type": error_type,
                "error_message": str(e),
            }

            base_message = f"Error in {context.method}: {error_type}: {str(e)}"

            logger.exception(base_message, **error_log_details)

            # Transform and re-raise
            transformed_error = map_error_to_mcp_error(e)
            raise transformed_error
