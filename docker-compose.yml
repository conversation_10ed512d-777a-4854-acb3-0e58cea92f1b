networks:
  pmcp_network:
    driver: bridge

services:
  postgres_db:
    container_name: pmcp_postgres_db
    image: postgres:17.6-alpine
    restart: unless-stopped
    env_file:
      - .env
    ports:
      - ${DB_PORT}:5432
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U ${DB_USER} -d ${DB_NAME}']
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - pmcp_network
