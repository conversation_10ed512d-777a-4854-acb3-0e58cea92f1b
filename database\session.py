from collections.abc import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from utils.json_custom import custom_json_serializer

from action_server.core.config import Config


ASYNC_POOL_CONFIG = dict(
    echo=False, pool_size=10, max_overflow=10, pool_recycle=1800, pool_pre_ping=True, json_serializer=custom_json_serializer
)

db_async_engine = create_async_engine(Config.DB_URL, **ASYNC_POOL_CONFIG)
db_async_session_maker = async_sessionmaker(db_async_engine, expire_on_commit=False, autocommit=False, autoflush=False)

async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for getting a PII database session.

    Use this for regular database access where automatic transaction handling is desired.
    The session will be automatically closed when the request is complete.
    """
    async with db_async_session_maker() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            raise e
        finally:
            await session.close()

__all__ = [
    "get_async_db",
    "db_async_session_maker",
    "db_async_engine",
]