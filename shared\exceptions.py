class ResourceUnavailableError(Exception):
    """Custom exception for temporary resource failures"""
    pass

class ContentTooLargeError(Exception):
    """Custom exception for payload size limits"""
    pass

class RequestCancelledError(Exception):
    """Custom exception for cancelled operations"""
    pass

class ParseError(Exception):
    """Custom exception for JSON parsing errors"""
    pass

class InvalidRequestError(Exception):
    """Custom exception for malformed requests"""
    pass

class MethodNotFoundError(Exception):
    """Custom exception for unknown methods"""
    pass