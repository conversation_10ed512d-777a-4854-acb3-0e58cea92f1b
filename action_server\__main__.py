import asyncio
from loguru import logger
from fastmcp import FastMCP
from starlette.requests import Request
from starlette.responses import JSONResponse

from action_server.core.config import Config
from action_server.core.logging import setup_logging
from action_server.tools import send_hello_tool
from action_server.middlewares import RequestLoggingMiddleware, ErrorHandlingMiddleware

instructions = (
    "This is the Provably Model Context Protocol (PMCP) Action Server. "
    "It acts as a trusted gateway for AI agents to interact with external systems. "
    "It handles agent identity, logs all actions for auditability, "
    "and ensures verifiable execution. Agents can use its tools for querying resources, "
    "executing trades, and managing their wallets. "
    "All actions performed through this server are persistently logged and "
    "provable by the PMCP Prover Server."
)

action_mcp = FastMCP(
    name="ActionServer",
    version="1.0.0",
    auth=None,
    lifespan=None,
    instructions=instructions,
    on_duplicate_tools="error",
    on_duplicate_resources="warn",
    on_duplicate_prompts="replace",
    include_fastmcp_meta=True,
)

@action_mcp.custom_route("/health", methods=["GET"])
async def health_check(request: Request) -> JSONResponse:
    return JSONResponse({"status": "healthy", "service": "pmcp-action-mcp-server"})

action_mcp.add_tool(send_hello_tool)

action_mcp.add_middleware(RequestLoggingMiddleware())
action_mcp.add_middleware(ErrorHandlingMiddleware())

async def main():
    
    # --- On Startup ---
    setup_logging()
    logger.info("Starting PMCP Action Server")
    logger.debug(f"Application starting in '{Config.ENVIRONMENT}' environment.")
    logger.debug(f"Project Name: {Config.PROJECT_NAME}")
    
    logger.info(f"{Config.PROJECT_NAME} v{Config.VERSION} started successfully in {Config.ENVIRONMENT} mode.")
    
    await action_mcp.run_async(
        transport="http", port=Config.MCP_PORT, show_banner=False, log_level="critical"
    )
    
    # --- On Shutdown ---
    logger.info("Application is shutting down...")


if __name__ == "__main__":
    asyncio.run(main())
